package main

import (
	"encoding/json"
	"fmt"
	"github.com/google/uuid"
	"github.com/spaolacci/murmur3"
	"log"
	"net/http"
	"net/http/httputil"
	"net/url"
	"os"
	"strconv"
	"time"
)

const (
	seed         = 0
	normalizer   = 100
	deviceCookie = "device_id"
)

var (
	experimentName     string
	experimentGroupA   string
	experimentGroupB   string
	experimentEventHit string
	soguURL            string
	proxyURL           string
	redirectURL        string
	splitPercent       int
	serviceTarget      *url.URL
	AppVersion         string
	browserDebugLogs   bool
)

func main() {
	var err error
	experimentName = getenv("EXPERIMENT_NAME", "monolit-mf-ab-test")
	experimentGroupA = getenv("EXPERIMENT_GROUP_A", "Monolit")
	experimentGroupB = getenv("EXPERIMENT_GROUP_B", "MF")
	experimentEventHit = getenv("EXPERIMENT_EVENT_HIT", "experiments.hit")
	redirectURL = getenv("REDIRECT_URL", "https://google.com")

	browserDebugLogsStr := getenv("BROWSER_DEBUG_LOGS", "false")
	browserDebugLogs, err = strconv.ParseBool(browserDebugLogsStr)
	if err != nil {
		log.Fatalf("Invalid BROWSER_DEBUG_LOGS: %v", err)
	}

	splitPercent, err = strconv.Atoi(getenv("SPLIT_PERCENT", "50"))
	if err != nil {
		log.Fatalf("Invalid SPLIT_PERCENT: %v", err)
	}

	proxyURL = getenv("PROXY_URL", "https://ya.ru") // nginx
	soguURL = getenv("SOGU_URL", "https://sogu-staging.sogu.dev.tripster.tech/events/")

	serviceTarget, err = url.Parse(proxyURL)
	if err != nil {
		log.Fatalf("Invalid SERVICE_URL: %v", err)
	}

	http.HandleFunc("/", splitHandler)
	log.Println("Splitter listening on :8080")
	log.Fatal(http.ListenAndServe(":8080", nil))
}

func splitHandler(w http.ResponseWriter, r *http.Request) {
	deviceID := getOrSetDeviceID(w, r)
	group := determineGroup(deviceID, experimentName, splitPercent)

	go sendAnalytics(r, deviceID, group)

	if group == experimentGroupB {
		http.Redirect(w, r, redirectURL, http.StatusFound)
		return
	}

	// Проксируем дальше
	proxy := httputil.NewSingleHostReverseProxy(serviceTarget)
	proxy.ServeHTTP(w, r)
}

func getCookie(r *http.Request, name string) string {
	cookie, err := r.Cookie(name)
	if err == nil && cookie.Value != "" {
		return cookie.Value
	}

	return ""
}

func getOrSetDeviceID(w http.ResponseWriter, r *http.Request) string {
	cookieValue := getCookie(r, deviceCookie)
	if cookieValue != "" {
		return cookieValue
	}

	newID := uuid.New().String()
	http.SetCookie(w, &http.Cookie{
		Name:  deviceCookie,
		Value: newID,
		Path:  "/",
	})
	return newID
}

func determineGroup(deviceID, groupID string, percentage int) string {
	hash := murmur3.Sum32WithSeed([]byte(fmt.Sprintf("%s:%s", groupID, deviceID)), seed)
	normalized := int(hash%normalizer) + 1
	if normalized <= percentage {
		return experimentGroupB
	}
	return experimentGroupA
}

func sendAnalytics(r *http.Request, deviceID, group string) {
	if soguURL == "" {
		return
	}

	gaClientID := getCookie(r, "_ga")
	yandexClientID := getCookie(r, "_ym_uid")

	type Params struct {
		DeviceID     string `json:"device_id"`
		Experiment   string `json:"experiment"`
		Variant      string `json:"variant"`
		WasGenerated bool   `json:"was_generated"`
	}

	type Event struct {
		AppVersion       string `json:"app_version"`
		EventName        string `json:"event_name"`
		Platform         string `json:"platform"`
		URL              string `json:"url"`
		BrowserDebugLogs bool   `json:"browser_debug_logs"`
		GAClientID       string `json:"ga_client_id"`
		YAClientID       string `json:"ya_client_id"`
		UserAgent        string `json:"user_agent"`
		DeviceID         string `json:"device_id"`
		DT               int64  `json:"dt"`
		Params           Params `json:"params"`
	}

	event := Event{
		AppVersion:       AppVersion,
		EventName:        experimentEventHit,
		Platform:         "web",
		URL:              r.URL.String(),
		BrowserDebugLogs: browserDebugLogs,
		GAClientID:       gaClientID,
		YAClientID:       yandexClientID,
		UserAgent:        r.Header.Get("User-Agent"),
		DeviceID:         deviceID,
		DT:               time.Now().Unix(),
		Params: Params{
			DeviceID:     deviceID,
			Experiment:   experimentName,
			Variant:      group,
			WasGenerated: false,
		},
	}

	body, err := json.Marshal([]Event{event})
	log.Printf("Sending analytics event: %+v", body)
	if err != nil {
		log.Printf("Failed to marshal sogu payload: %v", err)
		return
	}

	// Асинхронная отправка
	go func() {
		//resp, err := http.Post(soguURL, "application/json", bytes.NewBuffer(body))
		//if err != nil {
		//	log.Printf("Failed to send sogu analytics: %v", err)
		//	return
		//}
		//defer resp.Body.Close()
		//log.Printf("Sent SOGU analytics for %s (%s)", deviceID, group)
	}()
}

func getenv(key, fallback string) string {
	val := os.Getenv(key)
	if val == "" {
		return fallback
	}
	return val
}
